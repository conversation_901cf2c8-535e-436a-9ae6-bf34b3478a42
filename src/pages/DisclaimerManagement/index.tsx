import {
  CheckCircleOutlined,
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  FileTextOutlined,
  HistoryOutlined,
  PlusOutlined,
  QuestionCircleOutlined,
  SearchOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import { navigate } from '@classification/admin-solution';
import type { IExportCSVConfig } from '@classification/export-csv';
import ExportCSV from '@classification/export-csv';
import { useTableHooks } from '@classification/table';
import type { ISearchProps } from '@classification/table/types/hooks/useSearchbarHooks';
import {
  Button,
  message,
  Modal,
  Space,
  Tooltip,
} from 'antd';
import React, {
  useCallback,
  useMemo,
  useRef,
  useState,
} from 'react';

import UploadFile from 'src/components/Upload';
import { mockDisclaimers } from 'src/mocks';
import Status from './Status';
import style from './style.module.scss';
import DisclaimerView from './View';

const exportFields: IExportCSVConfig = {
  cols: {
    name: 'Disclaimer Name',
    description: 'Description',
    approval: 'Approval',
    status: 'Status',
  },
  fileName: 'disclaimers',
}

const DisclaimerManagement = () => {
  const [searchValue, setSearchValue] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedDisclaimer, setSelectedDisclaimer] = useState<any>(null);

  const usePreData = useRef(false);
  const preData = useRef<{
    data: any[];
    hasNext?: boolean;
  }>({
    data: [],
  });
  const searchProps: ISearchProps = useMemo(() => {
    return {
      resetBtn: {
        children: 'Reset',
      },
      searchBtn: {
        type: 'primary',
        children: 'Search',
        icon: <SearchOutlined/>,
      },
      form: {
        className: style.searchForm,
        layout: 'inline',
        items: [
          {
            inputType: 'Input',
            name: 'name',
            label: '',
            inputCompProps: {
              placeholder: 'Please search disclaimer name',
              value: searchValue,
              onChange: (e: React.ChangeEvent<HTMLInputElement>) => setSearchValue(e.target.value),
            },
          },
        ],
      },
    }
  }, [searchValue]);

  const getData = useCallback(async (data: any) => {
    console.log('data', data)
    try {
      if (usePreData.current) {
        return preData.current;
      }
      const data = {
        data: mockDisclaimers,
        nextOffset: '11',
      }
      preData.current = data;
      return data;
    } finally {
      usePreData.current = false;
    }
  }, []);

  const handleView = (record: any) => {
    setSelectedDisclaimer(record);
    setIsModalVisible(true);
  };

  const handleEdit = (record: any) => {
    navigate(`/product/disclaimers-management/detail?id=${record.id}`);
    message.info(`Edit disclaimer: ${ record.name }`);
  };

  const handleDelete = (record: any) => {
    Modal.confirm({
      width: 300,
      className: style.deleteConfirmModal,
      icon: <></>,
      title: '',
      okButtonProps: {
        type: 'primary',
      },
      content: <div className={ style.deleteConfirm }>
        <div className={ style.deleteConfirmIcon }>
          <WarningOutlined/>
        </div>
        <div className={ style.title }>
          Delete Disclaimer?
        </div>
        <div className={ style.content }>
          Are you sure you want to delete
          <div className={ style.name }>
            "{ record.name }"?
          </div>
          <div>
            This Action cannot be undone.
          </div>
        </div>

      </div>,
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: () => {
        message.success(`Deleted disclaimer: ${ record.name }`);
      },
    });
  };

  const handleNewDisclaimer = () => {
    message.info('Create new disclaimer');
  };

  const handleUpdateLog = () => {
    message.info('Update log');
    navigate('/product/disclaimers-management/log');
  };

  const renderApprovalStatus = (record: any) => {
    const { type } = record;
    if (type === 'approved') {
      return (
        <Space>
          <CheckCircleOutlined style={ { color: '#52c41a' } }/>
        </Space>
      );
    }
    const onChange = async (data?: { hash?: string; filename?: string }) => {
      preData.current.data.forEach(item => {
        if (item.id === record.id) {
          item.approvalFileAddr = data;
        }
      });

      usePreData.current = true;
      refresh();
    }
    return (
      <UploadFile value={ record.approvalFileAddr } onChange={ onChange }/>
    );
  };

  const getExportData = async () => {
    // return getData();
  }


  const { table, refresh } = useTableHooks({
    titleBar: {
      title: (
        <div className={ style.titleBar }>
          <div className={ style.actionButtons }>
            <Button
              icon={ <PlusOutlined/> }
              type="primary"
              onClick={ handleNewDisclaimer }
            >
              New Disclaimer
            </Button>
            <Button
              icon={ <HistoryOutlined/> }
              onClick={ handleUpdateLog }
            >
              Update Log
            </Button>
            <ExportCSV
              requestFunc={ getExportData }
              exportConfig={ exportFields }
              icon={ <FileTextOutlined/> }
            >
              Export List
            </ExportCSV>
          </div>
        </div>
      ),
    },
    searchProps,
    table: {
      rowKey: 'name',
      getData,
      pagination: {
        pageSize: 50,
        showSizeChanger: false,
        showQuickJumper: false,
      },
      dataFilterConfig: {
        countOffset: true,
        offsetInitValue: 0,
      },
      columns: [
        {
          title: 'Disclaimer Name',
          dataIndex: 'name',
          key: 'name',
          width: 200,
        },
        {
          title: 'Disclaimer Description',
          dataIndex: 'description',
          key: 'description',
          ellipsis: true,
        },
        {
          title: (
            <Space>
              Approval
              <Tooltip title="Approval status information">
                <QuestionCircleOutlined/>
              </Tooltip>
            </Space>
          ),
          dataIndex: 'approvalFileAddr',
          key: 'approvalFileAddr',
          width: 300,
          render: (approval_file_addr: any, record: any) => renderApprovalStatus(record),
        },
        {
          title: 'Status',
          dataIndex: 'disclaimerStatus',
          key: 'disclaimerStatus',
          width: 100,
          render: (status: string) => <Status status={status}/>,
        },
        {
          title: 'Action',
          key: 'action',
          width: 200,
          render: (_, record: any) => (
            <Space size="middle">
              <Tooltip title="View">
                <EyeOutlined
                  className={ style.actionIcon }
                  onClick={ () => handleView(record) }
                />
              </Tooltip>
              <Tooltip title="Edit">
                <EditOutlined
                  className={ style.actionIcon }
                  onClick={ () => handleEdit(record) }
                />
              </Tooltip>
              <Tooltip title="Delete">
                <DeleteOutlined
                  className={ style.actionIcon }
                  onClick={ () => handleDelete(record) }
                />
              </Tooltip>
            </Space>
          ),
        },
      ],
    },
  });

  return (
    <div className={ style.container }>
      <h1 className={ style.pageTitle }>Local Disclaimers List</h1>
      { table }
      <DisclaimerView
        visible={ isModalVisible }
        disclaimer={ selectedDisclaimer }
        onClose={ () => setIsModalVisible(false) }
      />
    </div>
  );

}

export default DisclaimerManagement;
