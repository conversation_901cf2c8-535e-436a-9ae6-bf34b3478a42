import { Button, Form, Input, message,Switch, Typography } from 'antd';
import { FormItemProps } from 'antd/lib/form/FormItem';
import React, { useState } from 'react';

import ConfigurableCard from 'src/components/ConfigurableCard';
import UploadFile from 'src/components/Upload';
import style from '../style.module.scss';

const { TextArea } = Input;
const { Text } = Typography;

// 表单验证规则
export const validationRules = {
  disclaimerName: [
    { required: true, message: 'Please enter disclaimer name' },
    { max: 100, message: 'Disclaimer name cannot exceed 100 characters' },
    { whitespace: true, message: 'Disclaimer name cannot be empty' },
  ],
  disclaimerDescription: [
    { required: true, message: 'Please enter disclaimer description' },
    { max: 1000, message: 'Description cannot exceed 1000 characters' },
    { whitespace: true, message: 'Description cannot be empty' },
  ],
};

interface GeneralInformationProps {
  isEdit: boolean;
  initialValues?: any;
}

const GeneralInformation: React.FC<GeneralInformationProps> = ({
  initialValues = {
    name: '',
    description: '',
    status: true,
    legalDocument: undefined,
  },
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  // 保存General Information
  const handleSave = async () => {
    try {
      // 验证基础字段
      await form.validateFields();

      setLoading(true);

      // 模拟API调用保存基本信息
      await new Promise(resolve => setTimeout(resolve, 1000));

      setIsEdit(false)
      if (isEdit) {
        // 编辑模式：只保存，不跳转
        message.success('General information updated successfully.');
      } else {
        // 新增模式：保存并跳转到Advanced Settings
        message.success('General information saved successfully. You can now configure advanced settings.');
      }

    } catch {

    } finally {
      setLoading(false);
    }
  };

  return (
    <ConfigurableCard
      header={{
        title: 'General Information',
        extra: (
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            {
              isEdit ? <>
                <Button onClick={() => setIsEdit(false)}>
                  Cancel
                </Button>
                <Button type="primary" onClick={handleSave} loading={loading}>
                  Save
                </Button>
              </> : (
                <Button onClick={() => setIsEdit(true)}>
                  Edit
                </Button>
              )
            }
          </div>
        )
      }}
      className={style.sectionCard}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={initialValues}
      >
        <>
          <Form.Item required name="name" label="Disclaimer Name" rules={validationRules.disclaimerName}>
            <Input />
          </Form.Item>

          <Form.Item required name="description" label="Disclaimer Description" rules={validationRules.disclaimerDescription}>
            <TextArea
              placeholder="Enter display description"
              rows={4}
            />
          </Form.Item>

          <Form.Item name="status" valuePropName="checked">
            <Switch checkedChildren="Active" unCheckedChildren="Inactive"  />
          </Form.Item>

          <Form.Item label="Approval Document" name="legalDocument">
            <UploadFile
              mode="edit"
            />
          </Form.Item>
        </>
      </Form>
    </ConfigurableCard>
  );
};

export default GeneralInformation;
