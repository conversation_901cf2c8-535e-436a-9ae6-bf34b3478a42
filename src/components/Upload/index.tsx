import {
  LoadingOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import {
  Button,
  message,
  Spin,
  Upload,
} from 'antd';
import type {
  UploadProps as IUploadProps,
} from 'antd/lib/upload';
import { UploadFile } from 'antd/lib/upload/interface';
import cx from 'classnames';
import React, {
  useEffect,
  useState,
} from 'react';

import style from 'src/components/Upload/style.module.scss';
import {
  downloadFile,
  mmsManagement,
} from 'src/constants/mms';

const UploadFile = ({ value, onChange, className, mode = 'edit' }: {
  value?: {
    hash: string;
    filename?: string;
  },
  onChange?: (data?: {
    hash?: string;
    filename?: string;
  }) => Promise<void>,
  className?: string;
  mode?: 'view' | 'edit';
  children?: React.ReactNode;
}) => {
  const [fileData, setFile] = useState<{
    uid: string;
    name: string;
    url: string;
  } | undefined>();
  const [loading, setLoading] = useState(false);
  const [downloading, setDownloading] = useState(false);

  useEffect(() => {
    const getFile = async () => {
      const result = await mmsManagement.getGenericDownloadLink({
        id: value.hash,
      });
      const { ok, url } = result as { ok: boolean; url: string };
      if (ok) {
        setFile(value ? {
          uid: value.hash,
          name: value.filename!,
          url: url as string,
        } : undefined);
      }

    };
    getFile()
  }, [value]);

  const onRemove = async () => {
    setLoading(true);
    await onChange?.();
    setLoading(false);
  }

  const customRequest = (async (options) => {
    const file = options.file;

    setLoading(true);
    try {
      const res = await mmsManagement.uploadGenericFile(file as File);
      if (res?.id) {
        options.onSuccess?.({
          path: res?.id,
        });
        onChange?.({
          filename: (file as UploadFile)?.name,
          hash: res?.id,
        });
      }
    } catch (err) {
      (file as unknown as { status: string }).status = 'error';
      message.error(
        typeof err === 'string' ? err : (err as Error).message,
        5,
      );
      options.onError?.(err as Error);
    } finally {
      setLoading(false)
    }

  }) as IUploadProps['customRequest']
  const download = async (e: React.MouseEvent<HTMLElement>) => {
    e.preventDefault();
    e.stopPropagation();

    if (downloading) {
      return;
    }
    try {
      setDownloading(true);
      await downloadFile(fileData?.url!, fileData?.name!, '');
    } catch {
      message.error('Download failed');
    } finally {
      setDownloading(false);
    }
  };
  return <div className={ style.uploadContainer }>
    {
      fileData && <div className={ style.uploadItem }>
        <div className={ cx(style.download, downloading ? style.downloading : '', 'download') } onClick={ download }>
          { fileData.name }
        </div>
        { downloading && <Spin className={ style.downloading } indicator={ <LoadingOutlined spin/> } size="small"/> }
      </div>
    }
    <Upload
      accept=".pdf,.png,.jpg,.jpeg"
      className={ className }
      disabled={ loading }
      maxCount={1}
      fileList={ [] }
      onRemove={onRemove}
      customRequest={customRequest}
    >
      {
        mode ==='edit'  && (fileData ? <Button type="link" loading={ loading }>Change</Button>
          :
          <>
            <UploadOutlined/>
            <Button type="link" loading={ loading }>Upload</Button>
          </>)
      }
    </Upload>
  </div>;
}
export default UploadFile;
